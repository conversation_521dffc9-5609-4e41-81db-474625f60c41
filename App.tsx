
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import useLocalStorage from './hooks/useLocalStorage';
import { CoffeeBag } from './types';
import { calculateDaysSince, formatDate, getBagDuration } from './utils/dateUtils';
import { formatCurrency, CURRENCIES, getDefaultCurrency } from './utils/formattingUtils';

// --- Icons (Heroicons & Lucide) ---
const CoffeeCupIcon = (props: React.SVGProps<SVGSVGElement>) => ( // Lucide 'Coffee' icon
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M10 2v2"/><path d="M14 2v2"/><path d="M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1"/><path d="M6 2v2"/></svg>
);
const PlusIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5" {...props}>
    <path fillRule="evenodd" d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5 0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z" clipRule="evenodd" />
  </svg>
);
const CalendarDaysIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-3.75h.008v.008H12v-.008z" />
  </svg>
);
const CurrencyDollarIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);
const ArchiveBoxIcon = (props: React.SVGProps<SVGSVGElement>) => (
 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
  <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10.5 18.75h3M3.75 7.5h16.5M12 12.75V15m0 0V12.75m0 2.25H9.75m2.25 0H14.25M3.75 7.5L4.5 3.75A1.5 1.5 0 016 2.25h12a1.5 1.5 0 011.5 1.5L20.25 7.5" />
</svg>
);
const SparklesIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456Z" />
  </svg>
);
const TrashIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
  </svg>
);
const InformationCircleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
  </svg>
);

// GA Measurement ID - REPLACE WITH YOURS!
const GA_MEASUREMENT_ID = "G-9120QR47S5";

// Cloudflare Workers
const INCREMENT_ENDPOINT =
  import.meta.env.VITE_CF_INCREMENT_ENDPOINT ||                // Vite
  process.env.REACT_APP_CF_INCREMENT_ENDPOINT ||               // CRA
  process.env.NEXT_PUBLIC_CF_INCREMENT_ENDPOINT ||             // Next
  'https://cuppacounter.qgnj6svnm7.workers.dev/api/increment';

// --- Reusable UI Components ---
interface StatCardProps {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
  className?: string;
  valueClassName?: string;
}
const StatCard: React.FC<StatCardProps> = ({ label, value, icon, className = '', valueClassName = '' }) => (
  <div className={`bg-white p-4 rounded-xl shadow-lg flex flex-col items-center justify-center text-center ${className}`}>
    {icon && <div className="mb-2 text-amber-600">{icon}</div>}
    <span className={`text-2xl font-bold text-amber-700 ${valueClassName}`}>{value}</span>
    <span className="text-sm text-stone-500 mt-1">{label}</span>
  </div>
);

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}
const CustomButton: React.FC<ButtonProps> = ({ children, variant = 'primary', size = 'md', className = '', ...props }) => {
  const baseStyles = 'font-semibold rounded-2xl focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-150 ease-in-out inline-flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed';
  const variantStyles = {
    primary: 'bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500',
    secondary: 'bg-stone-200 text-stone-700 hover:bg-stone-300 focus:ring-stone-400',
    danger: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500',
    ghost: 'bg-transparent text-amber-600 hover:bg-amber-100 focus:ring-amber-500',
  };
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl',
  };
  return (
    <button
      className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size] || ''} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'md' | 'lg';
}
const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md' }) => {
  if (!isOpen) return null;

  const sizeClasses = {
    md: 'max-w-md',
    lg: 'max-w-lg'
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-75 backdrop-blur-sm flex items-center justify-center p-4 z-[60] transition-opacity duration-300 ease-in-out"
      onClick={onClose} // Close on overlay click
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div 
        className={`bg-white rounded-2xl shadow-2xl w-full ${sizeClasses[size]} flex flex-col max-h-[90vh] transform transition-all duration-300 ease-in-out scale-100`}
        onClick={e => e.stopPropagation()} // Prevent closing when clicking inside modal
      >
        <div className="flex justify-between items-center p-4 sm:p-6 border-b border-stone-200 flex-shrink-0">
          <h2 id="modal-title" className="text-xl font-bold text-amber-800">{title}</h2>
          <CustomButton variant="ghost" size="sm" onClick={onClose} aria-label="Close modal" className="p-1 -mr-2 text-stone-500 hover:text-stone-700">
            ✕
          </CustomButton>
        </div>
        <div className="p-4 sm:p-6 overflow-y-auto flex-grow">
          {children}
        </div>
      </div>
    </div>
  );
};


// --- Main App Component ---
function App() {
  const [coffeeBags, setCoffeeBags] = useLocalStorage<CoffeeBag[]>('coffeeBags', []);
  const [showNewBagModal, setShowNewBagModal] = useState(false);
  const [animateCupCount, setAnimateCupCount] = useState(false);

  // Cookie Consent and Privacy Policy State
  const [showCookieConsentBanner, setShowCookieConsentBanner] = useState(false);
  const [cookieConsentAccepted, setCookieConsentAccepted] = useLocalStorage<boolean | null>('cookieConsentAccepted', null);
  const [showPrivacyPolicyModal, setShowPrivacyPolicyModal] = useState(false);

  const currentBag = useMemo(() => coffeeBags.find(bag => bag.isCurrent), [coffeeBags]);
  const previousBags = useMemo(() => coffeeBags.filter(bag => !bag.isCurrent).sort((a, b) => new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime()), [coffeeBags]);

  const initializeAnalytics = useCallback(() => {
    if (document.getElementById('ga-script')) return; 

    const script = document.createElement('script');
    script.id = 'ga-script';
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    script.onload = () => {
      // @ts-ignore
      window.dataLayer = window.dataLayer || [];
      // @ts-ignore
      function gtag(...args: any[]){ window.dataLayer.push(args);}
      gtag('js', new Date());
      gtag('config', GA_MEASUREMENT_ID);
      console.log('Google Analytics initialized.');
    };
  }, []);

  useEffect(() => {
    if (cookieConsentAccepted === null) { 
      setShowCookieConsentBanner(true);
    } else if (cookieConsentAccepted === true) {
      initializeAnalytics();
      setShowCookieConsentBanner(false);
    } else { 
      setShowCookieConsentBanner(false);
    }
  }, [cookieConsentAccepted, initializeAnalytics]);


  const handleAcceptCookies = useCallback(() => {
    setCookieConsentAccepted(true);
    setShowCookieConsentBanner(false);
    initializeAnalytics(); 
  }, [setCookieConsentAccepted, initializeAnalytics]);

  const handleAddCoffee = useCallback(async () => {
    if (!currentBag) return;

    if (cookieConsentAccepted === true) {
        // Increment global coffee count via Cloudflare Worker
        try {
            const response = await fetch(INCREMENT_ENDPOINT, { method: 'POST' });
            if (!response.ok) {
                console.error("Failed to increment global coffee count. Status:", response.status);
            }
        } catch (error) {
            console.error("Error calling increment endpoint:", error);
        }
    }

    setCoffeeBags(prevBags =>
      prevBags.map(bag =>
        bag.id === currentBag.id ? { ...bag, cupsConsumed: bag.cupsConsumed + 1 } : bag
      )
    );
    setAnimateCupCount(true);
    setTimeout(() => setAnimateCupCount(false), 300);
  }, [currentBag, setCoffeeBags, cookieConsentAccepted]);

  const handleOpenNewBagModal = useCallback(() => setShowNewBagModal(true), []);
  const handleCloseNewBagModal = useCallback(() => setShowNewBagModal(false), []);

  const handleSaveNewBag = useCallback((name: string | undefined, price: number, purchaseDate: string, currency: string) => {
    setCoffeeBags(prevBags => {
      const updatedBags = prevBags.map(bag => 
        bag.isCurrent ? { ...bag, isCurrent: false, endDate: new Date().toISOString() } : bag
      );
      const newBag: CoffeeBag = {
        id: Date.now().toString(),
        name: name || undefined,
        price,
        purchaseDate,
        currency,
        cupsConsumed: 0,
        isCurrent: true,
      };
      return [...updatedBags, newBag];
    });
    handleCloseNewBagModal();
  }, [setCoffeeBags, handleCloseNewBagModal]);

  const handleDeleteBag = useCallback((bagId: string) => {
    if (window.confirm("Are you sure you want to delete this bag record? This action cannot be undone.")) {
      setCoffeeBags(prevBags => prevBags.filter(bag => bag.id !== bagId));
    }
  }, [setCoffeeBags]);

  const costPerCupValue = useMemo(() => {
    if (!currentBag || currentBag.cupsConsumed === 0) return null;
    return currentBag.price / currentBag.cupsConsumed;
  }, [currentBag]);

  const displayCostPerCup = useMemo(() => {
    if (!currentBag || !currentBag.currency) return "N/A"; 
    return formatCurrency(costPerCupValue, currentBag.currency);
  }, [costPerCupValue, currentBag]);

  const numericDaysActive = useMemo(() => {
    if (!currentBag || !currentBag.purchaseDate) return NaN; 
    return calculateDaysSince(currentBag.purchaseDate);
  }, [currentBag]);

  const daysSincePurchase = useMemo(() => {
    if (!currentBag) return "N/A";
    const days = numericDaysActive;
    if (isNaN(days)) return "N/A";
    if (days === 0) return "Today";
    return `${days} day${days === 1 ? '' : 's'} ago`;
  }, [currentBag, numericDaysActive]);

  const averageCoffeesPerDayText = useMemo(() => {
    if (!currentBag || !currentBag.purchaseDate || currentBag.cupsConsumed <= 0) return null;
    const cups = currentBag.cupsConsumed;
    const days = numericDaysActive;
    if (isNaN(days) || days < 0) return null;
    if (days === 0) return `That's ${cups} coffee${cups === 1 ? '' : 's'} so far today. Yay!`;
    const average = cups / days;
    const formattedAverage = average.toFixed(1);
    const displayAverage = formattedAverage.endsWith('.0') ? parseInt(formattedAverage).toString() : formattedAverage;
    return `That's about ${displayAverage} coffee${parseFloat(displayAverage) !== 1.0 ? 's' : ''} a day. yay!`;
  }, [currentBag, numericDaysActive]);

  // --- Render Sub-Components ---
  const NewBagFormComponent: React.FC<{ onSave: (name: string | undefined, price: number, purchaseDate: string, currency: string) => void; onCancel: () => void }> = ({ onSave, onCancel }) => {
    const [name, setName] = useState('');
    const [price, setPrice] = useState('');
    const [purchaseDate, setPurchaseDate] = useState(new Date().toISOString().split('T')[0]);
    const [currency, setCurrency] = useState(getDefaultCurrency());
    const [error, setError] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      const numPrice = parseFloat(price);
      if (isNaN(numPrice) || numPrice <= 0) { setError('Please enter a valid price.'); return; }
      if (!purchaseDate) { setError('Please select a purchase date.'); return; }
      if (!currency) { setError('Please select a currency.'); return; }
      setError('');
      onSave(name.trim() || undefined, numPrice, purchaseDate, currency);
    };
    
    return (
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="bagName" className="block text-sm font-medium text-stone-700">Coffee Bag Name (Optional)</label>
          <input type="text" id="bagName" value={name} onChange={e => setName(e.target.value)} className="mt-1 block w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm" placeholder="e.g., Morning Glory Blend" />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="bagPrice" className="block text-sm font-medium text-stone-700">Bag Price</label>
            <input type="number" id="bagPrice" value={price} onChange={e => setPrice(e.target.value)} step="0.01" min="0.01" required className="mt-1 block w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm" placeholder="e.g., 15.99"/>
          </div>
          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-stone-700">Currency</label>
            <select id="currency" value={currency} onChange={e => setCurrency(e.target.value)} required className="mt-1 block w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm bg-white">
              {CURRENCIES.map(c => (<option key={c.code} value={c.code}>{c.name}</option>))}
            </select>
          </div>
        </div>
        <div>
          <label htmlFor="purchaseDate" className="block text-sm font-medium text-stone-700">Purchase Date</label>
          <input type="date" id="purchaseDate" value={purchaseDate} onChange={e => setPurchaseDate(e.target.value)} max={new Date().toISOString().split('T')[0]} required className="mt-1 block w-full px-3 py-2 border border-stone-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm"/>
        </div>
        {error && <p className="text-sm text-red-600">{error}</p>}
        <div className="flex justify-end space-x-3 pt-2">
          <CustomButton type="button" variant="secondary" onClick={onCancel}>Cancel</CustomButton>
          <CustomButton type="submit" variant="primary">Save Bag</CustomButton>
        </div>
      </form>
    );
  };

  const CookieConsentBanner: React.FC = () => {
    if (!showCookieConsentBanner) return null;
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-stone-800 text-white p-4 shadow-lg z-50 transform transition-transform duration-300 ease-out translate-y-0">
        <div className="container mx-auto max-w-4xl flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
          <p className="text-sm text-stone-300 leading-relaxed">
            Hi there! Your coffee data stays on your device. I use Google Analytics for general traffic insights and anonymously count global coffees for fun.
          </p>
          <div className="flex-shrink-0 space-x-3">
            <CustomButton size="sm" variant="ghost" className="text-amber-400 hover:bg-stone-700" onClick={() => setShowPrivacyPolicyModal(true)}>
              Learn More
            </CustomButton>
            <CustomButton size="sm" variant="primary" className="bg-amber-500 hover:bg-amber-600" onClick={handleAcceptCookies}>
              Accept
            </CustomButton>
          </div>
        </div>
      </div>
    );
  };

  const PrivacyPolicyModalContent: React.FC = () => (
    <div className="space-y-4 text-sm text-stone-700">
      <h3 className="font-semibold text-amber-700">How Cuppa Counter Works:</h3>
      <ul className="list-disc list-inside space-y-2 pl-2">
        <li><strong>No Database, No Login for Your Bag Details:</strong> All your coffee bag details (price, date, cups) are stored exclusively in your browser's local storage on your device. I don't have a central database for this, and you don't need an account. It's all yours!</li>
        <li><strong>Anonymous Global Coffee Count:</strong> When you add a coffee (and if you've accepted analytics), I anonymously send a signal to our server to increment a global counter of all coffees enjoyed by Cuppa Counter users. This is for fun, global statistics and is not tied to any of your personal data or specific bag information.</li>
        <li><strong>Google Analytics:</strong> I use standard Google Analytics to understand general website traffic (e.g., how many users visit, what features are popular, general geographic regions of visitors). This data is aggregated and anonymized by Google. It helps us improve the app for everyone.</li>
      </ul>
      <p><strong>In short:</strong> Your detailed coffee bag data stays on your device. Google Analytics helps us understand general traffic. A simple, anonymous "tick" is sent to count coffees globally. I do not sell your data.</p>
      <p>By using Cuppa Counter and accepting, you agree to these practices. Happy brewing!</p>
    </div>
  );


  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-100 via-orange-50 to-amber-100 text-stone-800 flex flex-col items-center p-4 sm:p-6 selection:bg-amber-500 selection:text-white"> {/* Increased pb for cookie banner */}
      <header className="w-full max-w-2xl text-center my-6 sm:my-8">
        <h1 className="text-4xl sm:text-5xl font-bold text-amber-700 flex items-center justify-center space-x-3">
          <CoffeeCupIcon className="w-10 h-10 sm:w-12 sm:h-12 text-amber-600" />
          <span>Cuppa Counter</span>
        </h1>
        <p className="text-stone-500 mt-2">Your personal coffee cost companion.</p>
      </header>

      <main className="w-full max-w-2xl space-y-8">
        {currentBag ? (
          <section aria-labelledby="current-bag-heading" className="bg-white/80 backdrop-blur-md p-6 rounded-2xl shadow-xl space-y-6">
            <div className="text-center">
              <h2 id="current-bag-heading" className="text-xl font-semibold text-amber-800">
                {currentBag.name ? `Current Bag: ${currentBag.name}` : 'Current Coffee Bag'}
              </h2>
              <p className="text-sm text-stone-500">Purchased: {formatDate(currentBag.purchaseDate)}</p>
            </div>
            
            <div className="flex flex-col items-center space-y-4">
              <CustomButton 
                onClick={handleAddCoffee} 
                variant="primary" 
                className="w-full max-w-xs h-24 text-2xl rounded-full shadow-lg transform hover:scale-105 active:scale-95 flex flex-col items-center justify-center py-4 px-6"
                aria-label="Add a coffee to the count"
              >
                <span className={`block transition-transform duration-300 ${animateCupCount ? 'scale-150' : 'scale-100'}`}>
                  {currentBag.cupsConsumed}
                </span>
                <span className="text-lg font-normal mt-1">Add Coffee</span>
              </CustomButton>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4">
              <StatCard label="Cost per Cup" value={displayCostPerCup} icon={<CurrencyDollarIcon className="w-8 h-8"/>} valueClassName={displayCostPerCup === "N/A" ? "text-xl" : ""} />
              <StatCard label="Days Active" value={daysSincePurchase} icon={<CalendarDaysIcon className="w-8 h-8"/>} valueClassName={daysSincePurchase === "N/A" ? "text-xl" : ""} />
            </div>
            {averageCoffeesPerDayText && (
              <p className="text-center text-sm text-stone-600 mt-3">
                {averageCoffeesPerDayText}
              </p>
            )}
            
            <CustomButton 
              onClick={handleOpenNewBagModal} 
              variant="ghost" 
              size="sm"
              className="mt-4 text-amber-700 hover:bg-amber-100 font-semibold"
            >
              <SparklesIcon className="w-5 h-5 mr-2"/> Start New Bag & Archive
            </CustomButton>
          </section>
        ) : (
          <section className="bg-white/80 backdrop-blur-md p-8 rounded-2xl shadow-xl text-center space-y-4">
            <h2 className="text-2xl font-semibold text-amber-800">Welcome to Cuppa Counter!</h2>
            <p className="text-stone-600">Let's get started by adding your first coffee bag.</p>
            <CustomButton onClick={handleOpenNewBagModal} variant="primary" size="lg">
              <PlusIcon className="w-5 h-5 mr-2" /> Add First Coffee Bag
            </CustomButton>
          </section>
        )}

        {previousBags.length > 0 && (
          <section aria-labelledby="previous-bags-heading" className="space-y-4">
            <h2 id="previous-bags-heading" className="text-2xl font-semibold text-amber-800 text-center mb-6 flex items-center justify-center gap-2">
              <ArchiveBoxIcon className="w-7 h-7" /> Previous Coffee Bags
            </h2>
            {previousBags.map(bag => {
              const finalCostPerCupValue = bag.cupsConsumed > 0 ? (bag.price / bag.cupsConsumed) : null;
              const displayFinalCostPerCup = formatCurrency(finalCostPerCupValue, bag.currency);
              const displayBagPrice = formatCurrency(bag.price, bag.currency);
              const duration = getBagDuration(bag);
              return (
                <div key={bag.id} className="bg-white/70 backdrop-blur-md p-5 rounded-xl shadow-lg space-y-1 relative">
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-semibold text-amber-700 mb-1">{bag.name || `Bag from ${formatDate(bag.purchaseDate)}`}</h3>
                    <CustomButton 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeleteBag(bag.id)}
                      className="text-red-500 hover:bg-red-100 p-1 absolute top-3 right-3" 
                      aria-label={`Delete bag ${bag.name || `from ${formatDate(bag.purchaseDate)}`}`}
                    >
                      <TrashIcon className="w-5 h-5"/>
                    </CustomButton>
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-3 gap-y-1 text-sm pr-10"> 
                    <p><strong className="text-stone-600">Purchased:</strong> {formatDate(bag.purchaseDate)}</p>
                    {bag.endDate && <p><strong className="text-stone-600">Finished:</strong> {formatDate(bag.endDate)}</p>}
                    {duration && <p><strong className="text-stone-600">Duration:</strong> {duration}</p>}
                    <p><strong className="text-stone-600">Total Cups:</strong> {bag.cupsConsumed}</p>
                    <p><strong className="text-stone-600">Bag Price:</strong> {displayBagPrice}</p>
                    <p><strong className="text-stone-600">Cost/Cup:</strong> {displayFinalCostPerCup}</p>
                  </div>
                </div>
              );
            })}
          </section>
        )}
      </main>

      <Modal isOpen={showNewBagModal} onClose={handleCloseNewBagModal} title={currentBag ? "Start a New Coffee Bag" : "Add Your First Coffee Bag"}>
        <NewBagFormComponent onSave={handleSaveNewBag} onCancel={handleCloseNewBagModal} />
      </Modal>

      <Modal isOpen={showPrivacyPolicyModal} onClose={() => setShowPrivacyPolicyModal(false)} title="Cookie & Privacy Policy" size="lg">
        <PrivacyPolicyModalContent />
      </Modal>
      
      <CookieConsentBanner />

      <footer className="w-full max-w-2xl text-center text-stone-500 text-xs mt-12 py-4 border-t border-amber-200">
        <p>&copy; {new Date().getFullYear()} Cuppa Counter. Happy brewing!</p>
        <p className="mt-1 md:mt-2">
          Fueling this app? Consider fueling its creator! ☕{' '}
          <a 
            href="https://buymeacoffee.com/martindoubravsky" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-amber-600 hover:text-amber-700 font-semibold underline"
          >
            Buy me a coffee
          </a>
        </p>
        <p className="mt-1 md:mt-2">
          <button 
            onClick={() => setShowPrivacyPolicyModal(true)}
            className=" hover:text-amber-700 font-normal underline"
          >
            Cookie Policy
          </button>
        </p>
      </footer>
    </div>
  );
}

export default App;
