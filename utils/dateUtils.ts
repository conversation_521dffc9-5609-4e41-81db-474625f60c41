
import { CoffeeBag } from '../types';

export const calculateDaysSince = (dateString: string): number => {
  const pastDate = new Date(dateString);
  const now = new Date();
  // Disregard time and timezone components for day difference
  const utcPast = Date.UTC(pastDate.getFullYear(), pastDate.getMonth(), pastDate.getDate());
  const utcNow = Date.UTC(now.getFullYear(), now.getMonth(), now.getDate());
  const differenceInMilliseconds = utcNow - utcPast;
  return Math.floor(differenceInMilliseconds / (1000 * 60 * 60 * 24));
};

export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const getBagDuration = (bag: CoffeeBag): string | null => {
  if (!bag.endDate) return null;
  const startDate = new Date(bag.purchaseDate);
  const endDate = new Date(bag.endDate);
  const differenceInMilliseconds = endDate.getTime() - startDate.getTime();
  const days = Math.max(1, Math.ceil(differenceInMilliseconds / (1000 * 60 * 60 * 24))); // Ensure at least 1 day
  return `${days} day${days === 1 ? '' : 's'}`;
};
