

export const CURRENCIES = [
  { code: 'USD', name: 'USD ($)', locale: 'en-US' },
  { code: 'EUR', name: 'EUR (€)', locale: 'de-DE' }, // Using de-DE for Euro, can be any Eurozone locale
  { code: 'GBP', name: 'GBP (£)', locale: 'en-GB' },
  { code: 'CZK', name: 'CZK (Kč)', locale: 'cs-CZ' },
];

export const getDefaultCurrency = () => CURRENCIES[0].code; // Default to USD

export const formatCurrency = (value: number | null | undefined, currencyCode: string, locale?: string): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return "N/A";
  }

  const selectedCurrency = CURRENCIES.find(c => c.code === currencyCode);
  const effectiveLocale = locale || selectedCurrency?.locale || navigator.language || 'en-US';

  try {
    return new Intl.NumberFormat(effectiveLocale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  } catch (error) {
    console.error("Error formatting currency:", error);
    // Fallback display if Intl.NumberFormat fails for some reason (e.g. invalid currency code)
    return `${currencyCode} ${value.toFixed(2)}`;
  }
};