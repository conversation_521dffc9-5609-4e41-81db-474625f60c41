# Cuppa Counter - Deployment Guide

## Cloudflare Workers Deployment (Recommended)

### First-time Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Login to Cloudflare (if not already logged in):**
   ```bash
   npx wrangler login
   ```

3. **Deploy to Cloudflare Workers:**
   ```bash
   npm run deploy
   ```

### Regular Deployment

After making changes, simply run:
```bash
npm run deploy
```

This will:
- Build your project (`npm run build`)
- Deploy to Cloudflare Workers (`npx wrangler deploy`)

### Local Development with Cloudflare

To test your app locally with Cloudflare Workers environment:
```bash
npm run cf:dev
```

## Configuration

### Cloudflare Workers Configuration (`wrangler.toml`)

```toml
name = "cuppa-counter"
compatibility_date = "2023-12-01"

[site]
bucket = "./dist"
```

### Custom Domain (Optional)

To use a custom domain, add this to `wrangler.toml`:
```toml
routes = [
  { pattern = "cuppa-counter.yourdomain.com/*", zone_name = "yourdomain.com" }
]
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run deploy` - Build and deploy to Cloudflare Workers
- `npm run cf:dev` - Test locally with Cloudflare Workers environment

## Alternative: Manual Static Hosting

## What's Included in the Build

The `dist` folder contains:
- `index.html` - The main HTML file
- `assets/` folder with:
  - `index-[hash].js` - All JavaScript code (including React)
  - `manifest-[hash].json` - PWA manifest
  - `apple-touch-icon-[hash].png` - App icon
- All paths are relative, so it works in any directory

## Deployment Options

### Option 1: Root Domain
Upload `dist` contents to your domain root:
- Access: `https://yourdomain.com/`

### Option 2: Subdirectory
Upload `dist` folder as a subdirectory:
- Access: `https://yourdomain.com/cuppa-counter/`

### Option 3: Subdomain
Upload `dist` contents to a subdomain:
- Access: `https://cuppa.yourdomain.com/`

## Troubleshooting

### If you see a blank page:
1. Check browser console for errors (F12 → Console)
2. Ensure all files in `dist/assets/` were uploaded
3. Check that your server serves `.js` files with correct MIME type
4. Verify the `index.html` file was uploaded correctly

### Common server requirements:
- Static file hosting (no server-side processing needed)
- Support for `.js`, `.json`, `.png` files
- HTTPS recommended (but not required)

## File Structure After Upload
```
your-server-directory/
├── index.html
└── assets/
    ├── index-unVTcsuo.js
    ├── manifest-DRcssPIu.json
    └── apple-touch-icon-IEv3V7Qc.png
```

## Notes
- The app is completely self-contained (no external dependencies except Tailwind CSS from CDN)
- Works offline after first load (PWA features)
- All data is stored locally in the browser
- No database or server-side code required
