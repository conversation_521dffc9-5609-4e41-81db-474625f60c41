<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cuppa Counter - Track Your Coffee Habits</title>

  <!-- SEO Meta Tags -->
  <meta name="description" content="Cuppa Counter: Track your coffee consumption, calculate cost per cup, manage coffee bag purchases, and see global coffee stats! Minimalistic, modern, and fun.">
  <meta name="keywords" content="coffee, counter, tracker, cost per cup, coffee bag, pwa, brewing, espresso, caffeine, productivity, coffee log, coffee journal">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://cuppacounter.com/"> <!-- Replace with your actual app URL -->
  <meta property="og:title" content="Cuppa Counter - Track Your Coffee Habits">
  <meta property="og:description" content="Track coffee consumption, cost per cup, and manage purchases. Minimalistic, modern, and fun!">
  <meta property="og:image" content="https://cuppacounter.com/og-image.png"> <!-- Replace with URL to an engaging image -->

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://cuppacounter.com/"> <!-- Replace with your actual app URL -->
  <meta property="twitter:title" content="Cuppa Counter - Track Your Coffee Habits">
  <meta property="twitter:description" content="Track coffee consumption, cost per cup, and manage purchases. Minimalistic, modern, and fun!">
  <meta property="twitter:image" content="https://cuppacounter.com/og-image.png"> <!-- Replace with URL to an engaging image -->


  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Google Fonts: Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- iOS PWA Meta Tags -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default"> 
  <meta name="apple-mobile-web-app-title" content="Cuppa Counter">
  <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png">

  <!-- Theme Color for browsers that support it -->
  <meta name="theme-color" content="#F59E0B">

  <style>
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; /* Default sans-serif stack */
    }
    h1, h2, h3 {
      font-family: 'Poppins', sans-serif;
    }
    /* Custom scrollbar for a more polished look */
    ::-webkit-scrollbar {
      width: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #c4a78c; /* A coffee-themed color */
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a1887f;
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
</head>
<body class="bg-amber-50">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
          })
          .catch(error => {
            console.log('ServiceWorker registration failed: ', error);
          });
      });
    }
  </script>
</body>
</html>