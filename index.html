<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cuppa Counter</title>
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- iOS PWA Meta Tags -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default"> 
  <meta name="apple-mobile-web-app-title" content="Cuppa Counter">
  <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png">

  <!-- Theme Color for browsers that support it -->
  <meta name="theme-color" content="#F59E0B">

  <style>
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    /* Custom scrollbar for a more polished look */
    ::-webkit-scrollbar {
      width: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #c4a78c; /* A coffee-themed color */
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a1887f;
    }
  </style>

</head>
<body class="bg-amber-50">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('./sw.js')
          .then(registration => {
            console.log('ServiceWorker registration successful with scope: ', registration.scope);
          })
          .catch(error => {
            console.log('ServiceWorker registration failed: ', error);
          });
      });
    }
  </script>
</body>
</html>