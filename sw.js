const CACHE_NAME = 'cuppa-counter-cache-v1';
// IMPORTANT: Update this list with actual files generated by your build process
// For esm.sh, caching strategy might be complex. This SW caches main page and known local assets.
// It will also cache CDN resources on first fetch if network is available.
const urlsToCache = [
  '/',
  '/index.html', // if you serve index.html directly
  // '/index.js', // Assuming your TSX compiles to index.js at root, adjust if different
  // Add other static assets like CSS files, local images, fonts if any.
  // Example: '/styles/main.css', '/images/logo.svg'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        // Add core assets that are absolutely necessary for the app shell
        return cache.addAll(urlsToCache.filter(url => !url.startsWith('http'))); // Only cache local assets initially
      })
      .catch(error => {
        console.error('Failed to cache urls during install:', error);
      })
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return response
        if (response) {
          return response;
        }

        // Not in cache - fetch from network
        return fetch(event.request).then(
          networkResponse => {
            // Check if we received a valid response
            if (!networkResponse || networkResponse.status !== 200 || networkResponse.type === 'error') {
              return networkResponse;
            }

            // IMPORTANT: Clone the response. A response is a stream
            // and because we want the browser to consume the response
            // as well as the cache consuming the response, we need
            // to clone it so we have two streams.
            const responseToCache = networkResponse.clone();

            caches.open(CACHE_NAME)
              .then(cache => {
                // Cache the new resource if it's a GET request and not a chrome-extension URL
                if (event.request.method === 'GET' && !event.request.url.startsWith('chrome-extension://')) {
                    cache.put(event.request, responseToCache);
                }
              });

            return networkResponse;
          }
        ).catch(error => {
          console.log('Fetch failed; returning offline page instead.', error);
          // Optionally, return a fallback offline page if a specific asset isn't found
          // For now, it will just fail if network is down and asset not cached.
          // return caches.match('/offline.html'); 
        });
      })
  );
});

self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Optional: Listen for messages from client to skip waiting
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
