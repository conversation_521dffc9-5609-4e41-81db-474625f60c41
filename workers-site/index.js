import { getAssetFromKV } from '@cloudflare/kv-asset-handler'

const DEBUG = false
const COFFEE_KEY = 'TOTAL'

// ———————————————————————————————
// very small CORS helper
const cors = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
}

addEventListener('fetch', event => {
  event.respondWith(handleEvent(event).catch(err => {
    if (DEBUG) {
      return new Response(err.stack, { status: 500, headers: cors })
    }
    return new Response('Internal Error', { status: 500, headers: cors })
  }))
})

async function handleEvent(event) {
  const { request } = event
  const url = new URL(request.url)

  // 1) ────────────────────────────────────────
  // API ENDPOINTS:  /api/increment  |  /api/total
  // ------------------------------------------------
  if (url.pathname === "/api/increment" && request.method === "POST") {
    const total = await incrementCoffeeCount();
    return json({ total });
  }

  if (url.pathname === '/api/total' && request.method === 'GET') {
    const total = parseInt(await COFFEE_KV.get(COFFEE_KEY) || '0', 10)
    return json({ total })
  }

  if (request.method === 'OPTIONS') {
    // CORS pre-flight
    return new Response(null, { headers: cors })
  }

  // 2) ────────────────────────────────────────
  // Everything else → try to serve static assets
  // ------------------------------------------------
  try {
    const page = await getAssetFromKV(event, DEBUG ? { cacheControl: { bypassCache: true } } : {})
    const response = new Response(page.body, page)
    // security headers (leave these if you like them)
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('Referrer-Policy', 'unsafe-url')
    response.headers.set('Feature-Policy', 'none')
    return response
  } catch (_) {
    // honour the SPA “rewrite all unknown paths to index.html” fallback
    const notFound = await getAssetFromKV(event, {
      mapRequestToAsset: req => new Request(`${new URL(req.url).origin}/index.html`, req),
    })
    return new Response(notFound.body, { ...notFound, status: 200 })
  }
}

async function incrementCoffeeCount() {
  const raw = await COFFEE_KV.get(COFFEE_KEY, "text");  // "42", "null", "NaN", or null
  let current = parseInt(raw ?? "0", 10);
  // 🛡 sanity-check
  if (isNaN(current) || current < 0) current = 0;
  const next = current + 1;
  await COFFEE_KV.put(COFFEE_KEY, next.toString());       // always a string
  return next;
}

// helper: JSON response with CORS & content-type
function json(obj) {
  return new Response(JSON.stringify(obj), {
    headers: { 'Content-Type': 'application/json', ...cors },
  })
}