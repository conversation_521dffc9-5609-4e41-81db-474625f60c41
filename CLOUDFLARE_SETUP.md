# Cloudflare Workers Setup Guide

## Prerequisites

1. **Cloudflare Account**: You need a Cloudflare account (free tier works)
2. **Domain (Optional)**: If you want a custom domain, you need to have it managed by Cloudflare

## Step-by-Step Deployment

### 1. First-time Setup

```bash
# Install dependencies
npm install

# Login to Cloudflare (this will open a browser)
npx wrangler login
```

### 2. Deploy Your App

```bash
# Build and deploy in one command
npm run deploy
```

That's it! Your app will be deployed to a URL like:
`https://cuppa-counter.your-subdomain.workers.dev`

### 3. Custom Domain (Optional)

If you want to use your own domain:

1. **Add your domain to Cloudflare** (if not already done)
2. **Update `wrangler.toml`**:
   ```toml
   routes = [
     { pattern = "cuppa.yourdomain.com/*", zone_name = "yourdomain.com" }
   ]
   ```
3. **Deploy again**:
   ```bash
   npm run deploy
   ```

## Useful Commands

```bash
# Deploy to Cloudflare Workers
npm run deploy

# Test locally with Cloudflare Workers environment
npm run cf:dev

# View deployment logs
npx wrangler tail

# Check deployment status
npx wrangler deployments list
```

## Troubleshooting

### Authentication Issues
If you get authentication errors:
```bash
npx wrangler logout
npx wrangler login
```

### Build Issues
Make sure the build works locally first:
```bash
npm run build
npm run preview
```

### Domain Issues
- Make sure your domain is managed by Cloudflare
- Check that the zone_name in wrangler.toml matches your domain
- DNS changes can take up to 24 hours to propagate

## What Gets Deployed

- Your built React app (from the `dist` folder)
- A Cloudflare Worker that serves your static files
- Automatic HTTPS
- Global CDN distribution
- Automatic caching

## Cost

- **Free tier**: 100,000 requests per day
- **Paid tier**: $5/month for 10 million requests
- No bandwidth charges
- No storage charges for static sites
