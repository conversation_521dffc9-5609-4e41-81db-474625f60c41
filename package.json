{"name": "cuppa-counter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "deploy": "npm run build && npx wrangler deploy", "cf:dev": "npm run build && npx wrangler dev"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@cloudflare/kv-asset-handler": "^0.4.0", "@types/node": "^22.14.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "typescript": "~5.7.2", "vite": "^6.2.0", "wrangler": "^4.19.1"}}